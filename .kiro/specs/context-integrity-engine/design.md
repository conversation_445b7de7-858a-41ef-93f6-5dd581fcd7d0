# Design Document

## Overview

The Context Integrity Engine (CIE) is a modular, event-driven system that acts as an intelligent intermediary between AI code assistants and the codebase. Built with Rust for performance and reliability, the engine leverages Tree-sitter for syntactic analysis, vector databases for semantic similarity detection, and sophisticated LLM interaction patterns to maintain code integrity through proactive monitoring and intelligent debate mechanisms.

## Architecture

The CIE follows a microservices architecture with clear separation of concerns, enabling scalability and maintainability. The system operates in real-time, intercepting code generation requests and performing integrity checks before changes are applied to the codebase.

### High-Level Architecture Diagram

```mermaid
graph TB
    User[Developer] --> LLM[Code Assistant LLM]
    LLM --> ICO[Integrity Check Orchestrator]
    
    ICO --> CKB[Code Knowledge Base]
    ICO --> LLMD[LLM Debater]
    ICO --> CMA[Code Modifier & Archiver]
    
    CM[Codebase Monitor] --> CPI[Code Parser & Indexer]
    CPI --> CKB
    
    CKB --> VDB[(Vector Database)]
    CKB --> MDS[(Metadata Store)]
    
    LLMD --> LLM
    CMA --> Codebase[Actual Codebase]
    CM --> Codebase
    
    style ICO fill:#e1f5fe
    style CKB fill:#f3e5f5
    style LLMD fill:#fff3e0
```

## Components and Interfaces

### 1. Codebase Monitor (Rust)

**Purpose:** Continuously tracks file system changes and triggers parsing updates.

**Key Responsibilities:**
- Monitor file system events using the `notify` crate
- Filter relevant code files based on extensions and ignore patterns
- Trigger incremental updates to the Code Knowledge Base
- Handle batch processing for large-scale changes

**Interface:**
```rust
pub trait CodebaseMonitor {
    fn start_monitoring(&self, path: &Path) -> Result<(), MonitorError>;
    fn stop_monitoring(&self) -> Result<(), MonitorError>;
    fn register_change_handler(&self, handler: Box<dyn ChangeHandler>);
}

pub struct FileChangeEvent {
    pub path: PathBuf,
    pub change_type: ChangeType, // Created, Modified, Deleted
    pub timestamp: SystemTime,
}
```

### 2. Code Parser and Indexer (Rust + Tree-sitter)

**Purpose:** Parses source code into structured representations and generates embeddings.

**Key Responsibilities:**
- Generate Concrete Syntax Trees using Tree-sitter
- Extract structural information (functions, classes, methods)
- Generate semantic embeddings for code units
- Perform incremental parsing for efficiency

**Interface:**
```rust
pub trait CodeParser {
    fn parse_file(&self, path: &Path) -> Result<ParsedFile, ParseError>;
    fn extract_code_units(&self, parsed: &ParsedFile) -> Vec<CodeUnit>;
    fn generate_embedding(&self, code_unit: &CodeUnit) -> Result<Vec<f32>, EmbeddingError>;
}

pub struct CodeUnit {
    pub id: String,
    pub name: String,
    pub unit_type: CodeUnitType, // Function, Class, Method, etc.
    pub content: String,
    pub location: CodeLocation,
    pub signature: String,
    pub dependencies: Vec<String>,
}
```

### 3. Code Knowledge Base (Vector Database + Metadata Store)

**Purpose:** Central repository for codebase structural and semantic information.

**Components:**
- **Vector Database (Qdrant):** Stores high-dimensional embeddings for similarity search
- **Metadata Store (SQLite):** Stores detailed metadata and relationships

**Interface:**
```rust
pub trait CodeKnowledgeBase {
    fn store_code_unit(&self, unit: &CodeUnit, embedding: Vec<f32>) -> Result<(), StorageError>;
    fn find_similar(&self, embedding: Vec<f32>, threshold: f32) -> Result<Vec<SimilarityMatch>, SearchError>;
    fn get_code_unit(&self, id: &str) -> Result<Option<CodeUnit>, StorageError>;
    fn update_code_unit(&self, id: &str, unit: &CodeUnit) -> Result<(), StorageError>;
    fn delete_code_unit(&self, id: &str) -> Result<(), StorageError>;
}

pub struct SimilarityMatch {
    pub code_unit: CodeUnit,
    pub similarity_score: f32,
    pub match_type: MatchType, // Exact, High, Medium, Low
}
```

### 4. Integrity Check Orchestrator (Rust)

**Purpose:** Central decision-maker that coordinates integrity checks and manages the overall workflow.

**Key Responsibilities:**
- Receive and validate proposed code changes
- Orchestrate duplication, overwrite, and deletion checks
- Coordinate with LLM Debater for conflict resolution
- Make final decisions on code change approval

**Interface:**
```rust
pub trait IntegrityOrchestrator {
    fn check_proposed_change(&self, change: &ProposedChange) -> Result<IntegrityResult, CheckError>;
    fn resolve_conflicts(&self, conflicts: Vec<IntegrityConflict>) -> Result<Resolution, ResolutionError>;
}

pub struct ProposedChange {
    pub change_type: ChangeType,
    pub target_file: PathBuf,
    pub content: String,
    pub context: ChangeContext,
}

pub struct IntegrityResult {
    pub status: CheckStatus, // Approved, Flagged, Rejected
    pub conflicts: Vec<IntegrityConflict>,
    pub recommendations: Vec<String>,
}
```

### 5. LLM Debater (Rust + LLM API)

**Purpose:** Facilitates intelligent debates with the LLM when integrity issues are detected.

**Key Responsibilities:**
- Construct contextual prompts for detected issues
- Parse and evaluate LLM justifications
- Manage multi-turn debate conversations
- Escalate unresolved conflicts to users

**Interface:**
```rust
pub trait LLMDebater {
    fn initiate_debate(&self, conflict: &IntegrityConflict) -> Result<DebateSession, DebateError>;
    fn continue_debate(&self, session: &mut DebateSession, response: &str) -> Result<DebateStatus, DebateError>;
    fn evaluate_justification(&self, justification: &str) -> JustificationQuality;
}

pub struct DebateSession {
    pub conflict: IntegrityConflict,
    pub conversation_history: Vec<DebateMessage>,
    pub status: DebateStatus,
    pub max_turns: u32,
    pub current_turn: u32,
}
```

### 6. Code Modifier and Archiver (Rust)

**Purpose:** Applies validated changes to the codebase and manages code archiving.

**Key Responsibilities:**
- Apply approved code changes safely
- Archive previous versions of modified code
- Maintain audit trails for all changes
- Handle rollback operations when needed

**Interface:**
```rust
pub trait CodeModifier {
    fn apply_change(&self, change: &ValidatedChange) -> Result<ChangeResult, ModificationError>;
    fn archive_code(&self, unit: &CodeUnit, reason: &str) -> Result<ArchiveEntry, ArchiveError>;
    fn rollback_change(&self, change_id: &str) -> Result<(), RollbackError>;
}

pub struct ArchiveEntry {
    pub id: String,
    pub original_code: CodeUnit,
    pub archive_timestamp: SystemTime,
    pub reason: String,
    pub replacement_id: Option<String>,
}
```

## Data Models

### Core Data Structures

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeLocation {
    pub file_path: PathBuf,
    pub start_line: u32,
    pub end_line: u32,
    pub start_column: u32,
    pub end_column: u32,
}

#[derive(Debug, Clone)]
pub enum IntegrityConflict {
    Duplication {
        proposed_code: CodeUnit,
        existing_matches: Vec<SimilarityMatch>,
    },
    Overwrite {
        target_code: CodeUnit,
        proposed_changes: String,
        change_type: OverwriteType,
    },
    Deletion {
        target_code: CodeUnit,
        deletion_reason: Option<String>,
    },
}

#[derive(Debug, Clone)]
pub enum ChangeType {
    Addition,
    Modification,
    Deletion,
    Refactoring,
}
```

### Database Schema

**Vector Database (Qdrant):**
- Collections organized by programming language
- Vectors of 768 dimensions (using CodeBERT embeddings)
- Metadata payload includes code unit ID, file path, and basic properties

**Metadata Store (SQLite):**
```sql
CREATE TABLE code_units (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    unit_type TEXT NOT NULL,
    file_path TEXT NOT NULL,
    start_line INTEGER NOT NULL,
    end_line INTEGER NOT NULL,
    content_hash TEXT NOT NULL,
    signature TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE code_dependencies (
    id TEXT PRIMARY KEY,
    source_unit_id TEXT NOT NULL,
    target_unit_id TEXT NOT NULL,
    dependency_type TEXT NOT NULL,
    FOREIGN KEY (source_unit_id) REFERENCES code_units(id),
    FOREIGN KEY (target_unit_id) REFERENCES code_units(id)
);

CREATE TABLE archive_entries (
    id TEXT PRIMARY KEY,
    original_unit_id TEXT NOT NULL,
    archived_content TEXT NOT NULL,
    archive_reason TEXT NOT NULL,
    archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    replacement_unit_id TEXT
);
```

## Error Handling

### Error Categories

1. **System Errors:** File I/O, database connectivity, parsing failures
2. **Integrity Errors:** Conflicts detected during checks
3. **LLM Errors:** API failures, invalid responses, timeout issues
4. **User Errors:** Invalid input, permission issues

### Error Handling Strategy

```rust
#[derive(Debug, thiserror::Error)]
pub enum CIEError {
    #[error("Parse error: {0}")]
    Parse(#[from] ParseError),
    
    #[error("Storage error: {0}")]
    Storage(#[from] StorageError),
    
    #[error("LLM communication error: {0}")]
    LLMCommunication(#[from] LLMError),
    
    #[error("Integrity conflict: {conflict:?}")]
    IntegrityConflict { conflict: IntegrityConflict },
    
    #[error("System error: {message}")]
    System { message: String },
}
```

### Graceful Degradation

- **Vector Database Unavailable:** Fall back to text-based similarity using fuzzy matching
- **LLM API Unavailable:** Queue conflicts for later resolution, allow user override
- **Parse Errors:** Skip problematic files, log for manual review
- **Performance Issues:** Implement circuit breakers and rate limiting

## Testing Strategy

### Unit Testing

- **Component Isolation:** Each component tested independently with mocked dependencies
- **Edge Cases:** Test boundary conditions, malformed input, and error scenarios
- **Performance:** Benchmark critical paths like similarity search and parsing

### Integration Testing

- **End-to-End Workflows:** Test complete integrity check cycles
- **Database Integration:** Verify vector and metadata storage consistency
- **LLM Integration:** Test debate scenarios with mock LLM responses

### Test Data Strategy

```rust
// Test fixtures for consistent testing
pub struct TestCodebase {
    pub files: HashMap<PathBuf, String>,
    pub expected_units: Vec<CodeUnit>,
    pub similarity_pairs: Vec<(String, String, f32)>,
}

// Integration test scenarios
#[cfg(test)]
mod integration_tests {
    #[tokio::test]
    async fn test_duplication_detection_workflow() {
        // Test complete duplication detection and resolution
    }
    
    #[tokio::test]
    async fn test_overwrite_protection_workflow() {
        // Test overwrite detection and LLM debate
    }
}
```

### Performance Testing

- **Load Testing:** Simulate large codebases (100k+ lines)
- **Concurrency Testing:** Multiple simultaneous integrity checks
- **Memory Usage:** Monitor memory consumption during long-running operations
- **Latency Testing:** Ensure sub-2-second response times for integrity checks

The design emphasizes modularity, performance, and reliability while maintaining the flexibility to handle diverse programming languages and coding patterns. The event-driven architecture ensures real-time responsiveness, while the debate mechanism provides intelligent conflict resolution that learns from LLM reasoning patterns.