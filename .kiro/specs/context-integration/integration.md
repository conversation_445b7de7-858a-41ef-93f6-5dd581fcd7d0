# Context Engine Integration Document

## Overview

This document describes how the Context Engine and Context Integrity Engine work together to provide a comprehensive AI-assisted development experience. The integration maintains clear separation of concerns while enabling powerful synergies between general context management and specialized code integrity monitoring.

## Integration Architecture

```mermaid
graph TB
    subgraph "AI Assistant Layer"
        AI[AI Assistant<br/>Claude/GPT/etc]
    end
    
    subgraph "Context Engine"
        CM[Context Manager]
        CS[Context Store]
        CP[Context Processor]
        CR[Context Retriever]
    end
    
    subgraph "Context Integrity Engine"
        ICO[Integrity Check Orchestrator]
        CKB[Code Knowledge Base]
        LLMD[LLM Debater]
        CMA[Code Modifier & Archiver]
    end
    
    subgraph "Development Environment"
        IDE[IDE/Editor]
        FS[File System]
        GIT[Git Repository]
    end
    
    subgraph "Storage Layer"
        VDB[(Vector Database)]
        MDB[(Metadata Database)]
        CACHE[(Cache Layer)]
    end
    
    %% Main flow
    IDE --> CM
    CM --> AI
    AI --> ICO
    ICO --> AI
    AI --> IDE
    
    %% Context sharing
    CM -.->|Context Provider| ICO
    ICO -.->|Integrity Feedback| CM
    
    %% Storage sharing
    CS --> VDB
    CS --> MDB
    CKB --> VDB
    CKB --> MDB
    
    %% File system monitoring
    FS --> CM
    FS --> ICO
    
    %% Version control integration
    GIT --> CM
    
    style CM fill:#e1f5fe
    style ICO fill:#fff3e0
    style AI fill:#f3e5f5
```

## Integration Points

### 1. Context Provider Interface

The Context Engine provides contextual information to the Context Integrity Engine through a dedicated interface:

```rust
pub trait IntegrityContextProvider {
    /// Get broader project context for a specific file
    fn get_code_context(&self, file_path: &Path) -> Result<CodeContext, ContextError>;
    
    /// Find similar implementations across the project
    fn get_similar_implementations(&self, code_snippet: &str) -> Result<Vec<CodeMatch>, ContextError>;
    
    /// Get established coding patterns for the project
    fn get_project_patterns(&self, project_id: &str) -> Result<Vec<CodePattern>, ContextError>;
    
    /// Get user preferences relevant to code integrity
    fn get_integrity_preferences(&self, user_id: &str) -> Result<IntegrityPreferences, ContextError>;
}

impl IntegrityContextProvider for ContextManager {
    fn get_code_context(&self, file_path: &Path) -> Result<CodeContext, ContextError> {
        // Retrieve relevant context including:
        // - Related files and dependencies
        // - Recent conversation history about this file
        // - Documentation and comments
        // - Similar code patterns in the project
    }
    
    fn get_similar_implementations(&self, code_snippet: &str) -> Result<Vec<CodeMatch>, ContextError> {
        // Use semantic search to find similar code across:
        // - Current project
        // - Previous conversations
        // - Archived implementations
        // - External documentation
    }
}
```

### 2. Integrity Feedback Interface

The Context Integrity Engine provides feedback to the Context Engine about code integrity decisions:

```rust
pub trait IntegrityFeedbackReceiver {
    /// Record integrity decisions for learning
    fn record_integrity_decision(&self, decision: &IntegrityDecision) -> Result<(), ContextError>;
    
    /// Update context based on integrity conflicts
    fn update_conflict_context(&self, conflict: &IntegrityConflict) -> Result<(), ContextError>;
    
    /// Archive code that was legitimately replaced
    fn archive_replaced_code(&self, archive_entry: &ArchiveEntry) -> Result<(), ContextError>;
}

pub struct IntegrityDecision {
    pub conflict_type: ConflictType,
    pub decision: Decision, // Approved, Rejected, Modified
    pub justification: String,
    pub confidence_score: f32,
    pub context_factors: Vec<ContextFactor>,
}
```

### 3. Shared Storage Layer

Both engines share storage infrastructure while maintaining logical separation:

```rust
pub struct SharedStorageConfig {
    pub vector_db_url: String,
    pub metadata_db_path: PathBuf,
    pub context_collection: String,      // "context_general"
    pub integrity_collection: String,    // "context_integrity"
    pub shared_cache_size: usize,
}

// Separate collections in vector database
// - context_general: Conversations, documentation, preferences
// - context_integrity: Code units, similarity patterns, integrity decisions
// - context_shared: Project structure, file metadata, common patterns
```

## Integration Workflows

### 1. Code Generation with Integrity Checking

```mermaid
sequenceDiagram
    participant User
    participant IDE
    participant ContextEngine as CE
    participant AI
    participant IntegrityEngine as IE
    
    User->>IDE: Request code generation
    IDE->>CE: Get context for request
    CE->>CE: Retrieve relevant context
    CE->>IDE: Return context package
    IDE->>AI: Send request + context
    AI->>AI: Generate code
    AI->>IE: Proposed code changes
    IE->>CE: Request project context
    CE->>IE: Return code patterns & history
    IE->>IE: Perform integrity checks
    
    alt Integrity Issues Found
        IE->>AI: Debate integrity concerns
        AI->>IE: Provide justification
        IE->>CE: Record decision & reasoning
    end
    
    IE->>AI: Integrity decision
    AI->>IDE: Final code + integrity status
    IDE->>User: Present code with integrity info
```

### 2. Learning from Integrity Decisions

```mermaid
sequenceDiagram
    participant IntegrityEngine as IE
    participant ContextEngine as CE
    participant Storage
    
    IE->>CE: Record integrity decision
    CE->>CE: Analyze decision patterns
    CE->>Storage: Update user preferences
    CE->>Storage: Update project patterns
    CE->>CE: Adjust future context weighting
    
    Note over CE: Learning improves future<br/>context relevance and<br/>integrity predictions
```

### 3. Context-Aware Integrity Checking

The Context Engine enhances integrity checking by providing:

**Historical Context:**
- Previous conversations about similar code
- Past integrity decisions and their outcomes
- User preferences for code organization

**Project Context:**
- Established coding patterns and conventions
- Architecture decisions and constraints
- Related code and dependencies

**Semantic Context:**
- Intent behind code changes from conversation history
- Documentation and comments explaining code purpose
- Similar implementations and their evolution

## Configuration and Setup

### 1. Shared Configuration

```toml
# config.toml
[storage]
vector_db_url = "http://localhost:6333"
metadata_db_path = "./data/context.db"
cache_size_mb = 512

[context_engine]
max_conversation_history = 1000
embedding_model = "sentence-transformers/all-MiniLM-L6-v2"
summarization_threshold = 10000

[integrity_engine]
similarity_threshold = 0.85
max_debate_turns = 3
archive_retention_days = 90

[integration]
context_sharing_enabled = true
integrity_feedback_enabled = true
shared_learning_enabled = true
```

### 2. Service Initialization

```rust
pub struct IntegratedContextSystem {
    context_engine: ContextEngine,
    integrity_engine: IntegrityEngine,
    shared_storage: SharedStorage,
}

impl IntegratedContextSystem {
    pub fn new(config: &IntegratedConfig) -> Result<Self, InitError> {
        let shared_storage = SharedStorage::new(&config.storage)?;
        
        let context_engine = ContextEngine::new(
            config.context_engine.clone(),
            shared_storage.clone()
        )?;
        
        let integrity_engine = IntegrityEngine::new(
            config.integrity_engine.clone(),
            shared_storage.clone(),
            Box::new(context_engine.clone()) // Context provider
        )?;
        
        // Set up bidirectional communication
        context_engine.set_integrity_feedback_receiver(
            Box::new(integrity_engine.clone())
        );
        
        Ok(Self {
            context_engine,
            integrity_engine,
            shared_storage,
        })
    }
}
```

## Benefits of Integration

### 1. Enhanced Integrity Checking
- **Contextual Awareness:** Integrity decisions consider conversation history and user intent
- **Pattern Recognition:** Learn from project-specific coding patterns and preferences
- **Intelligent Debates:** Use conversation context to ask more relevant questions

### 2. Improved Context Relevance
- **Integrity Feedback:** Learn from integrity decisions to improve future context
- **Code Evolution Tracking:** Understand how code changes over time and why
- **Quality Indicators:** Use integrity scores to weight context relevance

### 3. Unified Learning
- **Cross-System Learning:** Insights from integrity checking improve general context
- **User Preference Evolution:** Understand user preferences through integrity decisions
- **Project Pattern Recognition:** Identify and reinforce successful code patterns

### 4. Reduced False Positives
- **Intent Understanding:** Distinguish between accidental and intentional code changes
- **Context-Aware Thresholds:** Adjust sensitivity based on project and user context
- **Historical Precedent:** Use past decisions to inform current integrity checks

## Performance Considerations

### 1. Shared Resource Management
- **Connection Pooling:** Share database connections between engines
- **Cache Coordination:** Coordinate cache invalidation and updates
- **Memory Management:** Balance memory usage between systems

### 2. Latency Optimization
- **Parallel Processing:** Run context retrieval and integrity checks concurrently
- **Predictive Caching:** Pre-load likely needed context based on user patterns
- **Incremental Updates:** Share incremental updates between systems

### 3. Scalability
- **Independent Scaling:** Scale engines independently based on load
- **Shared Infrastructure:** Leverage shared storage and caching infrastructure
- **Load Balancing:** Distribute requests across multiple instances

## Monitoring and Observability

### 1. Integration Metrics
- Context provider response times
- Integrity feedback processing latency
- Shared storage utilization
- Cross-system learning effectiveness

### 2. Quality Metrics
- Integrity decision accuracy with context
- Context relevance improvement over time
- User satisfaction with integrated experience
- False positive/negative rates

This integration design provides a powerful, cohesive system that leverages the strengths of both specialized integrity checking and comprehensive context management while maintaining clean architectural boundaries.